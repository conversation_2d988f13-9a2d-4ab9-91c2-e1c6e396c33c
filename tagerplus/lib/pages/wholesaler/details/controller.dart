import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';

class WholesalerDetailsController extends GetxController {
  final int wholesalerId;
  final wholesaler = Rxn<WholesalerOut>(null);
  final minCharge = Rxn<RegionMinChargeOut>(null);
  final items = <ItemOut>[].obs;

  WholesalerDetailsController({required this.wholesalerId});

  @override
  void onInit() {
    super.onInit();
    fetchData();
  }

  void fetchData() async {
    final response = await ApiService.to.apiClient.wholesalers.getWholesaler(
      wholesalerId,
    );
    wholesaler.value = response.data;
    final response2 = await ApiService.to.apiClient.wholesalers.listMinCharges(
      regionId: AuthService.to.stores.first.city?.id,
      wholesalerId: wholesaler.value!.id,
    );
    minCharge.value = response2.data!.first;
    final response3 = await ApiService.to.apiClient.items.listWholesalerItems(
      wholesalerId,
    );
    items.value = response3.data!.items;
  }
}
