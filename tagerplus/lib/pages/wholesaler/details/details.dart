import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/pages/wholesaler/details/controller.dart';

class WholesalerDetailsPage extends StatelessWidget {
  const WholesalerDetailsPage({super.key, required this.wholesalerId});

  final int wholesalerId;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final c = Get.put(WholesalerDetailsController(wholesalerId: wholesalerId));
    return Scaffold(body: Column(children: [
            
          ],
        ));
  }
}
