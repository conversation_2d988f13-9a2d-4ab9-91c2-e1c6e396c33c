import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/pages/auth/signup/controller.dart';

class SignupFormWidget extends StatelessWidget {
  final SignupController controller;
  final ThemeData theme;

  const SignupFormWidget({
    super.key,
    required this.controller,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Name field
          TextFormField(
            controller: controller.nameController,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            validator: controller.validateName,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: Colors.black87,
            ),
            decoration: _buildInputDecoration(
              labelText: 'full_name'.tr,
              prefixIcon: Icons.person_outline_rounded,
            ),
          ),
          const SizedBox(height: 16),

          // Phone field
          TextFormField(
            controller: controller.phoneNumberController,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.next,
            validator: controller.validatePhone,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: Colors.black87,
            ),
            decoration: _buildInputDecoration(
              labelText: 'phone_number'.tr,
              prefixIcon: Icons.phone_rounded,
            ),
          ),
          const SizedBox(height: 16),

          // Password field
          Obx(
            () => TextFormField(
              controller: controller.passwordController,
              obscureText: controller.isPasswordHidden.value,
              textInputAction: TextInputAction.next,
              validator: controller.validatePassword,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: Colors.black87,
              ),
              decoration: _buildInputDecoration(
                labelText: 'password'.tr,
                prefixIcon: Icons.lock_outline_rounded,
                suffixIcon: IconButton(
                  onPressed: controller.togglePasswordVisibility,
                  icon: Icon(
                    controller.isPasswordHidden.value
                        ? Icons.visibility_off_outlined
                        : Icons.visibility_outlined,
                    color: AppColors.primary.withValues(alpha: 0.7),
                    size: 22,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Confirm Password field
          Obx(
            () => TextFormField(
              controller: controller.confirmPasswordController,
              obscureText: controller.isConfirmPasswordHidden.value,
              textInputAction: TextInputAction.done,
              validator: controller.validateConfirmPassword,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: Colors.black87,
              ),
              decoration: _buildInputDecoration(
                labelText: 'confirm_password'.tr,
                prefixIcon: Icons.lock_outline_rounded,
                suffixIcon: IconButton(
                  onPressed: controller.toggleConfirmPasswordVisibility,
                  icon: Icon(
                    controller.isConfirmPasswordHidden.value
                        ? Icons.visibility_off_outlined
                        : Icons.visibility_outlined,
                    color: AppColors.primary.withValues(alpha: 0.7),
                    size: 22,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Terms and conditions checkbox
          Obx(
            () => Row(
              children: [
                Checkbox(
                  value: controller.agreeToTerms.value,
                  onChanged: (value) => controller.toggleTermsAgreement(),
                  activeColor: AppColors.primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () => controller.toggleTermsAgreement(),
                    child: Text(
                      'agree_to_terms'.tr,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  InputDecoration _buildInputDecoration({
    required String labelText,
    required IconData prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      labelText: labelText,
      prefixIcon: Icon(prefixIcon, color: AppColors.primary),
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: Colors.grey.shade50,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: BorderSide(color: AppColors.primary, width: 2.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: const BorderSide(color: Colors.red, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(28),
        borderSide: const BorderSide(color: Colors.red, width: 2.5),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
    );
  }
}
