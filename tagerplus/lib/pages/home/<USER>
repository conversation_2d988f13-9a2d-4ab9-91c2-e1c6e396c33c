import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';

class HomeProductsController extends GetxController {
  final wholesalers = <WholesalerOut>[].obs;
  final minCharges = <RegionMinChargeOut>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchWholesalers();
  }

  Future<void> fetchWholesalers() async {
    try {
      final res = await ApiService.to.apiClient.wholesalers.listWholesalers(
        regionId: AuthService.to.stores.first.city?.id,
      );
      wholesalers.value = res.data?.wholesalers ?? [];
      for (var element in res.data?.wholesalers ?? []) {
        final minCharge = await ApiService.to.apiClient.wholesalers
            .listMinCharges(regionId: element.regionId);
        minCharges.add(minCharge.data!.first);
      }
    } catch (e) {
      Get.snackbar('Error', e.toString());
    }
  }
}
