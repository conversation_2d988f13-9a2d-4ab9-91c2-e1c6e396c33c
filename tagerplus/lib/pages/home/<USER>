import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';

class HomeController extends GetxController {
  // Navigation state
  final RxInt currentNavIndex = 0.obs;

  // Wholesalers data
  final RxList<WholesalerOut> wholesalers = <WholesalerOut>[].obs;
  final RxMap<int, RegionMinChargeOut> minCharges =
      <int, RegionMinChargeOut>{}.obs;

  // Loading and error states
  final RxBool _isLoading = false.obs;
  final RxBool _hasError = false.obs;
  final RxString _errorMessage = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;
  int get currentIndex => currentNavIndex.value;

  @override
  void onInit() {
    super.onInit();
    fetchWholesalers();
  }

  // Navigation methods
  void onNavTap(int index) {
    currentNavIndex.value = index;
    // Handle navigation to different tabs
    switch (index) {
      case 0:
        // Home - already here
        break;
      case 1:
        // Search - TODO: Navigate to search page
        if (kDebugMode) {
          print('Navigate to Search page');
        }
        break;
      case 2:
        // Favorites - TODO: Navigate to favorites page
        if (kDebugMode) {
          print('Navigate to Favorites page');
        }
        break;
      case 3:
        // Orders - TODO: Navigate to orders page
        if (kDebugMode) {
          print('Navigate to Orders page');
        }
        break;
      case 4:
        // Profile - TODO: Navigate to profile page
        if (kDebugMode) {
          print('Navigate to Profile page');
        }
        break;
    }
  }

  // Data fetching methods
  Future<void> fetchWholesalers() async {
    if (_isLoading.value) return;

    _isLoading.value = true;
    _hasError.value = false;
    _errorMessage.value = '';

    try {
      // Get user's region from their store
      final userStore = AuthService.to.stores.isNotEmpty
          ? AuthService.to.stores.first
          : null;

      final regionId = userStore?.city?.id;

      if (kDebugMode) {
        print('Fetching wholesalers for region: $regionId');
      }

      final response = await ApiService.to.apiClient.wholesalers
          .listWholesalers(regionId: regionId);

      if (response.data?.wholesalers != null) {
        wholesalers.value = response.data!.wholesalers;

        // Fetch minimum charges for each wholesaler
        await _fetchMinCharges();

        if (kDebugMode) {
          print('Successfully loaded ${wholesalers.length} wholesalers');
        }
      } else {
        wholesalers.clear();
      }
    } catch (e) {
      _hasError.value = true;
      _errorMessage.value = e.toString();

      if (kDebugMode) {
        print('Error fetching wholesalers: $e');
        print('Stack trace: ${StackTrace.current}');
      }

      Get.snackbar('error'.tr, 'error_loading_wholesalers'.tr);
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _fetchMinCharges() async {
    minCharges.clear();

    for (final wholesaler in wholesalers) {
      try {
        // Note: The API structure might need adjustment based on actual implementation
        // This assumes there's a regionId property on wholesaler or we use the user's region
        final userStore = AuthService.to.stores.isNotEmpty
            ? AuthService.to.stores.first
            : null;
        final regionId = userStore?.city?.id;

        if (regionId != null) {
          final response = await ApiService.to.apiClient.wholesalers
              .listMinCharges(regionId: regionId, wholesalerId: wholesaler.id);

          if (response.data != null && response.data!.isNotEmpty) {
            minCharges[wholesaler.id] = response.data!.first;
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print(
            'Error fetching min charge for wholesaler ${wholesaler.id}: $e',
          );
        }
        // Continue with other wholesalers even if one fails
      }
    }
  }

  // Utility methods
  RegionMinChargeOut? getMinChargeForWholesaler(int wholesalerId) {
    return minCharges[wholesalerId];
  }

  void onWholesalerTap(WholesalerOut wholesaler) {
    // TODO: Navigate to wholesaler details page
    if (kDebugMode) {
      print('Tapped on wholesaler: ${wholesaler.title}');
    }

    Get.snackbar('product_details'.tr, 'product_details_coming_soon'.tr);
  }

  void onRetry() {
    fetchWholesalers();
  }

  Future<void> onRefresh() async {
    await fetchWholesalers();
  }
}
