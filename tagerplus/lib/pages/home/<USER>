import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/components/app_navigation_bar.dart';
import 'package:tagerplus/pages/home/<USER>';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return GetBuilder<HomeController>(
      init: HomeController(),
      builder: (controller) {
        return Scaffold(
          backgroundColor: AppColors.scaffoldBackground,
          body: SafeArea(
            child: Column(
              children: [
                // Header Section
                HeaderSection(theme: theme, size: size),

                // Main Content
                Expanded(
                  child: Obx(() {
                    if (controller.isLoading) {
                      return const LoadingSection();
                    }

                    if (controller.hasError) {
                      return ErrorSection(
                        errorMessage: controller.errorMessage,
                        onRetry: controller.onRetry,
                      );
                    }

                    if (controller.wholesalers.isEmpty) {
                      return const EmptySection();
                    }

                    return WholesalersSection(
                      controller: controller,
                      theme: theme,
                    );
                  }),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Obx(
            () => AppBottomNavigationBar(
              currentIndex: controller.currentIndex,
              onNavTap: controller.onNavTap,
            ),
          ),
        );
      },
    );
  }
}

// Header Section Widget
class HeaderSection extends StatelessWidget {
  final ThemeData theme;
  final Size size;

  const HeaderSection({super.key, required this.theme, required this.size});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDefaults.padding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أهلاً بك في تاجر بلس',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابحث عن تجار الجملة بالقرب منك',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.placeholder,
            ),
          ),
        ],
      ),
    );
  }
}

// Loading Section Widget
class LoadingSection extends StatelessWidget {
  const LoadingSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(color: AppColors.primary),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل تجار الجملة...',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.placeholder),
          ),
        ],
      ),
    );
  }
}

// Error Section Widget
class ErrorSection extends StatelessWidget {
  final String errorMessage;
  final VoidCallback onRetry;

  const ErrorSection({
    super.key,
    required this.errorMessage,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ أثناء تحميل تجار الجملة',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.placeholder,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Empty Section Widget
class EmptySection extends StatelessWidget {
  const EmptySection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.store_outlined, size: 80, color: AppColors.placeholder),
            const SizedBox(height: 24),
            Text(
              'لا يوجد تجار جملة متاحين حالياً',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'سوف يتم إضافة تجار جدد قريباً',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.placeholder,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Wholesalers Section Widget
class WholesalersSection extends StatelessWidget {
  final HomeController controller;
  final ThemeData theme;

  const WholesalersSection({
    super.key,
    required this.controller,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: controller.onRefresh,
      color: AppColors.primary,
      child: CustomScrollView(
        slivers: [
          // Section Header
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(AppDefaults.padding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تجار الجملة',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    '${controller.wholesalers.length} ${'تجار الجملة'}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.placeholder,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Wholesalers Grid
          SliverPadding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDefaults.padding,
            ),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.7,
                crossAxisSpacing: AppDefaults.padding,
                mainAxisSpacing: AppDefaults.padding,
              ),
              delegate: SliverChildBuilderDelegate((context, index) {
                final wholesaler = controller.wholesalers[index];
                final minCharge = controller.getMinChargeForWholesaler(
                  wholesaler.id,
                );

                return EnhancedWholesalerCard(
                  wholesaler: wholesaler,
                  minCharge: minCharge,
                  onTap: () => controller.onWholesalerTap(wholesaler),
                );
              }, childCount: controller.wholesalers.length),
            ),
          ),

          // Bottom spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: AppDefaults.padding * 2),
          ),
        ],
      ),
    );
  }
}

// Enhanced Wholesaler Card Widget
class EnhancedWholesalerCard extends StatelessWidget {
  final WholesalerOut wholesaler;
  final RegionMinChargeOut? minCharge;
  final VoidCallback onTap;

  const EnhancedWholesalerCard({
    super.key,
    required this.wholesaler,
    required this.minCharge,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: AppDefaults.borderRadius,
          boxShadow: AppDefaults.boxShadow,
          border: Border.all(color: Colors.grey[200]!, width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Logo Section
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppDefaults.radius),
                    topRight: Radius.circular(AppDefaults.radius),
                  ),
                ),
                child:
                    wholesaler.logoUrl != null && wholesaler.logoUrl!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(AppDefaults.radius),
                          topRight: Radius.circular(AppDefaults.radius),
                        ),
                        child: Image.network(
                          wholesaler.logoUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.store,
                              size: 40,
                              color: Colors.grey[400],
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                color: AppColors.primary,
                                value:
                                    loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            );
                          },
                        ),
                      )
                    : Icon(Icons.store, size: 40, color: Colors.grey[400]),
              ),
            ),

            // Content Section
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Wholesaler Name
                    Text(
                      wholesaler.title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // Description
                    if (wholesaler.description.isNotEmpty)
                      Text(
                        wholesaler.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppColors.placeholder,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                    const Spacer(),

                    // Minimum Order
                    if (minCharge != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.coloredBackground,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${'الحد الأدنى للطلب'}: ${minCharge!.minCharge.toStringAsFixed(0)} ${''}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${'لـ'} ${minCharge!.minItems} ${'قطعة'}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: AppColors.placeholder,
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          'اتصل للسعر',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppColors.placeholder,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
