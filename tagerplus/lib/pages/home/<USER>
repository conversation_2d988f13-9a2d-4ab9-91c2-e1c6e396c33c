import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';

class HomeProductsController extends GetxController {
  final wholesalers = <WholesalerOut>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchWholesalers();
  }

  Future<void> fetchWholesalers() async {
    try {
      final wholesalers = await ApiService.to.apiClient.wholesalers
          .listWholesalers(regionId: AuthService.to.stores.first.city?.id);
      this.wholesalers.value = wholesalers.data?.wholesalers ?? [];
    } catch (e) {
      Get.snackbar('Error', e.toString());
    }
  }
}
