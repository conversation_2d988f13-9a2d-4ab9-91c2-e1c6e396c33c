import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/api/models/wholesaler_out.dart';
import 'package:tagerplus/components/network_image.dart';

class WholesalerCard extends StatelessWidget {
  const WholesalerCard({
    super.key,
    required this.wholesaler,
    required this.onTap,
    this.hasCart = false,
    this.cartItemCount = 0,
  });

  final WholesalerOut wholesaler;
  final VoidCallback onTap;
  final bool hasCart;
  final int cartItemCount;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: hasCart ? AppColors.primary : Colors.grey[300]!,
            width: hasCart ? 2 : 1,
          ),
          boxShadow: AppDefaults.boxShadow,
        ),
        child: Stack(
          children: [
            Column(
              children: [
                // Logo section
                Expanded(
                  flex: 3,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child:
                        wholesaler.logoUrl != null &&
                            wholesaler.logoUrl!.isNotEmpty
                        ? NetworkImageWithLoader(
                            wholesaler.logoUrl!,
                            fit: BoxFit.fitHeight,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          )
                        : Icon(Icons.store, size: 40, color: Colors.grey[400]),
                  ),
                ),

                // Title section
                Expanded(
                  flex: 2,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          wholesaler.title,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: hasCart
                                    ? AppColors.primary
                                    : Colors.black,
                              ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (wholesaler.description.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            wholesaler.description,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: Colors.grey[600]),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Cart indicator
            if (hasCart)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    cartItemCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
