import 'package:get/get.dart';
import 'package:get_secure_storage/get_secure_storage.dart';

class LocalStorageService extends GetxService {
  static LocalStorageService get to => Get.find();

  final _box = GetSecureStorage(
    password: '********************************',
    container: 'tagerplus_storage',
  );

  Future<void> clear() async {
    await _box.erase();
  }

  @override
  void onClose() {
    _box.save();
    super.onClose();
  }

  String? get token => _box.read('token');
  set token(String? value) =>
      value != null ? _box.write('token', value) : _box.remove('token');

  String? get user => _box.read('user');
  set user(String? value) =>
      value != null ? _box.write('user', value) : _box.remove('user');

  String? get stores => _box.read('stores');
  set stores(String? value) =>
      value != null ? _box.write('stores', value) : _box.remove('stores');

  String? get currentStore => _box.read('currentStore');
  set currentStore(String? value) => value != null
      ? _box.write('currentStore', value)
      : _box.remove('currentStore');
}
