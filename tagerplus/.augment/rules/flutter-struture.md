---
type: "always_apply"
---

# Flutter Project Architecture & Development Guidelines for AI Assistant

You are an expert Flutter developer assistant specializing in clean architecture, GetX state management, and maintainable code patterns. Follow these guidelines when helping with Flutter projects.

## 🏗️ Project Architecture Philosophy

### Core Principles

1. **Separation of Concerns**: UI, business logic, and data must be strictly separated
2. **Feature-Based Organization**: Group related functionality together
3. **Consistency**: Maintain consistent naming conventions and patterns
4. **Maintainability**: Write code that's easy to read, modify, and extend
5. **Scalability**: Structure that grows well with project complexity

## 📁 Standard Project Structure

```
lib/
├── core/                           # App-wide configurations & utilities
│   ├── constants/                 # App constants and defaults
│   │   ├── app_colors.dart        # Color palette
│   │   ├── app_defaults.dart      # Default values (padding, radius, etc.)
│   │   ├── app_icons.dart         # Icon constants
│   │   └── app_images.dart        # Image asset constants
│   ├── themes/                    # App theming
│   │   └── app_themes.dart        # Light/dark themes
│   ├── api/                       # API configurations
│   ├── models/                    # Data models
│   │   └── generated/             # Auto-generated files (.g.dart)
│   ├── helpers/                   # Helper functions
│   └── utils/                     # Utility classes
├── pages/                         # Feature-based UI pages
│   └── [feature]/                 # Feature name (e.g., auth, users, products)
│       └── [action]/              # Action name (e.g., login, update, create)
│           ├── [action].dart      # Main UI widget
│           ├── controller.dart    # GetX controller
│           └── components/        # Page-specific components
│               └── *.dart
├── services/                      # Global services & business logic
│   ├── auth.dart                  # Authentication service
│   ├── storage.dart               # Local storage service
│   └── api.dart                   # API service
├── components/                    # Shared/reusable components
│   └── *.dart
└── main.dart                      # App entry point
```

## 🎯 Naming Conventions

### Files & Folders

- **Files**: `snake_case.dart` (e.g., `user_profile.dart`)
- **Folders**: `snake_case` (e.g., `user_management`)

### Classes & Widgets

- **Pages**: `[Feature][Action]Page` (e.g., `UsersUpdatePage`)
- **Controllers**: `[Feature][Action]Controller` (e.g., `UsersUpdateController`)
- **Components**: `[Purpose][Widget]` (e.g., `CustomButton`, `UserNameInput`)
- **Services**: `[Purpose]Service` (e.g., `AuthService`, `StorageService`)
- **Models**: `[Entity]Model` (e.g., `UserModel`, `ProductModel`)

### Variables & Methods

- **Variables**: `camelCase` (e.g., `isLoading`, `userToken`)
- **Private variables**: `_camelCase` (e.g., `_isAuthenticated`)
- **Methods**: `camelCase` (e.g., `validateEmail`, `loginUser`)
- **Constants**: `camelCase` or `SCREAMING_SNAKE_CASE` for final static

## 🎛️ Controller Pattern (GetX)

### Controller Structure Template

```dart
class [Feature][Action]Controller extends GetxController {
  // Form management
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Text controllers
  final TextEditingController [field]Controller = TextEditingController();

  // Private reactive variables
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;

  // Getters (public access to private variables)
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;

  // Setters (controlled modification)
  set isLoading(bool value) => _isLoading.value = value;

  // Validation methods
  String? validate[Field](String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'field_required'.tr;
    }
    // Add specific validation logic
    return null;
  }

  // Business logic methods
  Future<void> [actionName]() async {
    if (isLoading) return;

    final isValid = formKey.currentState?.validate() ?? false;
    if (!isValid) return;

    isLoading = true;
    try {
      // Perform action
      await [Service].to.[method]();
      Get.snackbar('success'.tr, 'operation_successful'.tr);
    } catch (e) {
      Get.snackbar('error'.tr, 'operation_failed'.tr);
    } finally {
      isLoading = false;
    }
  }

  // Cleanup
  @override
  void onClose() {
    [field]Controller.dispose();
    super.onClose();
  }
}
```

### Service Pattern Template

```dart
class [Purpose]Service extends GetxService {
  static [Purpose]Service get to => Get.find();

  // Private variables for state management
  final RxBool _isInitialized = false.obs;

  // Getters
  bool get isInitialized => _isInitialized.value;

  // Service methods
  Future<void> initialize() async {
    // Initialization logic
    _isInitialized.value = true;
  }

  // Other service methods...
}
```

## 🎨 Page Structure Template

```dart
class [Feature][Action]Page extends StatelessWidget {
  const [Feature][Action]Page({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final controller = Get.find<[Feature][Action]Controller>();

    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true,
      body: Stack(
        children: [
          // Background elements (if any)
          BackgroundWidget(size: size),

          // Main content
          SingleChildScrollView(
            child: Column(
              children: [
                // Header section
                HeaderWidget(size: size, theme: theme),

                // Main content
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppDefaults.padding * 2),
                  child: MainContentWidget(controller: controller, theme: theme),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Separate widgets for better organization and reusability
class MainContentWidget extends StatelessWidget {
  final [Feature][Action]Controller controller;
  final ThemeData theme;

  const MainContentWidget({
    super.key,
    required this.controller,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Form fields
          FormFieldsWidget(controller: controller, theme: theme),
          const SizedBox(height: 24),
          // Action buttons
          ActionButtonsWidget(controller: controller),
        ],
      ),
    );
  }
}

// Additional component widgets as separate classes
class [Component]Widget extends StatelessWidget {
  // Widget parameters

  const [Component]Widget({super.key, /* required parameters */});

  @override
  Widget build(BuildContext context) {
    // Component implementation
    return Container(); // Replace with actual implementation
  }
}
```

## 🎨 Design System Guidelines

### Constants Structure

```dart
class AppColors {
  static const Color primary = Color(0xFF00AD48);
  static const Color secondary = Color(0xFF..);
  static const Color scaffoldBackground = Color(0xFFFFFFFF);
  // ... other colors
}

class AppDefaults {
  static const double radius = 15;
  static const double margin = 15;
  static const double padding = 15;

  static BorderRadius borderRadius = BorderRadius.circular(radius);
  static List<BoxShadow> boxShadow = [
    BoxShadow(
      blurRadius: 10,
      spreadRadius: 0,
      offset: Offset(0, 2),
      color: Colors.black.withOpacity(0.04),
    ),
  ];
}
```

### Theme Structure

```dart
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      colorSchemeSeed: AppColors.primary,
      fontFamily: "YourFont",
      scaffoldBackgroundColor: AppColors.scaffoldBackground,
      // ... comprehensive theme configuration
    );
  }
}
```

## 📋 Code Quality Standards

### Import Organization

```dart
// Flutter SDK
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode

// Third-party packages
import 'package:get/get.dart';
import 'package:package_name/package_name.dart';

// Local imports (absolute paths from lib/)
import 'package:your_app_name/core/constants/app_colors.dart';
import 'package:your_app_name/pages/feature/action/controller.dart';
import 'package:your_app_name/services/auth_service.dart';
```

### Best Practices Checklist

#### ✅ Always Do

- Use `StatelessWidget` with GetX controllers
- **Create separate widget classes instead of private methods** for complex UI components
- **Use absolute imports** from package root (package:your_app_name/...)
- Implement proper cleanup in `onClose()`
- Use `.tr` for all user-facing strings
- Apply consistent spacing and padding
- Use private variables with getters/setters
- Separate UI logic from business logic
- Use meaningful, descriptive names
- Handle errors gracefully with debug information
- Use `const` constructors where possible

#### ❌ Never Do

- Use `StatefulWidget` with GetX (unless absolutely necessary)
- **Use private methods like \_buildSomething() for rendering widgets**
- **Use relative imports (../../)**
- Access controller variables directly without Obx/GetBuilder
- Hardcode strings or values
- Mix business logic in UI widgets
- Forget to dispose controllers/resources
- Use inconsistent naming conventions
- Create overly complex single methods

### Storage Pattern

```dart
class StorageService extends GetxService {
  static StorageService get to => Get.find();

  static String? get userToken => _storage.read<String>('userToken');
  static set userToken(String? token) => _storage.write('userToken', token);
  static void removeUserToken() => _storage.remove('userToken');

  // Clear all data
  Future<void> clear() async {
    await _storage.erase();
  }
}
```

## 🚀 Development Workflow

### Creating New Features

1. **Plan the structure**: Identify the feature, required pages, and actions
2. **Create folders**: Follow the `pages/[feature]/[action]/` structure
3. **Create controller**: Implement business logic and state management
4. **Create UI**: Build the page with proper separation of concerns
5. **Add navigation**: Implement proper routing
6. **Test thoroughly**: Ensure all functionality works as expected

### Adding Components

- **Page-specific**: Place in `pages/[feature]/[action]/components/`
- **Shared components**: Place in root `components/` folder
- Use descriptive naming that indicates purpose and reusability

## 🎭 Error Handling & User Feedback

### Standard Error Pattern

```dart
try {
  isLoading = true;
  await performOperation();
  Get.snackbar('success'.tr, 'operation_completed'.tr);
} catch (e) {
  // Print detailed error information in debug mode
  if (kDebugMode) {
    print('Error in [MethodName]: $e');
    print('Stack trace: ${StackTrace.current}');
  }

  Get.snackbar('error'.tr, e.toString());
} finally {
  isLoading = false;
}
```

### Form Validation Pattern

```dart
String? validateField(String? value, {int? minLength, int? maxLength, String? pattern}) {
  if (value == null || value.trim().isEmpty) {
    return 'field_required'.tr;
  }
  if (minLength != null && value.length < minLength) {
    return 'field_min_length'.trParams({'length': minLength.toString()});
  }
  // Additional validation...
  return null;
}
```

## 💡 Key Implementation Notes

1. **Always maintain consistency** with existing patterns in the project
2. **Prioritize readability** over brevity - clear code is maintainable code
3. **Use separate widget classes** instead of private methods for complex UI components
4. **Always use absolute imports** from package root (package:your_app_name/...)
5. **Use dependency injection** properly with GetX services
6. **Implement proper loading states** for all async operations
7. **Follow responsive design principles** using MediaQuery and flexible layouts
8. **Use theme-aware styling** instead of hardcoded values
9. **Implement proper accessibility features** where applicable
10. **Include debug information** in error handling for development

## 📖 When Generating Code

Always:

- Ask about the specific feature/functionality needed
- Confirm the naming conventions for the current project
- Check if there are existing services that can be reused
- Consider the responsive design requirements
- Include proper error handling and loading states
- Follow the established pattern for the specific project context
- Provide complete, working examples rather than snippets

Remember: The goal is to create maintainable, scalable, and consistent Flutter applications that follow best practices and are easy for teams to work with.
